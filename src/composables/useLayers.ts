import { ref, computed, reactive, nextTick } from 'vue';
import { GeoLayer, GroupLayer, LayerType } from '../types';
import { GeoServerService } from '../GeoServerService';
import { MapManager } from '../map';
import { getGeoServerCapabilitiesUrl } from '../config';

export function useLayerManager(mapManager: MapManager | null) {
  // Reactive state
  const layers = ref<GeoLayer[]>([]);
  const groups = ref<GroupLayer[]>([]);
  const isLoading = ref(false);
  const error = ref('');

  const geoServerService = new GeoServerService();

  // Computed properties for derived state (automatically reactive)
  const rootGroups = computed(() => {
    return groups.value.filter(group => !group.parent);
  });

  const ungroupedLayers = computed(() => {
    return layers.value.filter(layer => !layer.groupPath);
  });

  const fetchLayers = async () => {
    if (!mapManager) return;

    isLoading.value = true;
    error.value = '';

    try {
      console.log('Starting to fetch layers from GeoServer...');
      console.log('GeoServer URL:', getGeoServerCapabilitiesUrl());

      const fetchedLayers = await geoServerService.fetchLayers();
      console.log('Fetched layers count:', fetchedLayers.length);
      console.log('Fetched layers:', fetchedLayers);

      if (fetchedLayers.length > 0) {
        organizeLayersIntoGroups(fetchedLayers);
        fetchedLayers.forEach(layer => {
          layers.value.push(layer);
          mapManager.addLayer(layer);
        });

        console.log('Updated reactive layers:', layers.value);
        console.log('Updated root groups:', rootGroups.value);
        console.log('Updated ungrouped layers:', ungroupedLayers.value);
      } else {
        console.warn('No layers found in GeoServer');
        // Add test layers for debugging
        addTestLayers();
      }
    } catch (err) {
      console.error('Error fetching layers:', err);
      error.value = 'Error cargando capas desde GeoServer.';
      // Add test layers on error for debugging
      addTestLayers();
    } finally {
      isLoading.value = false;
    }
  };

  const addTestLayers = () => {
    const testLayers: GeoLayer[] = [
      {
        id: 'test_layer_1',
        name: 'Rivers',
        originalName: 'Hydrology/Rivers',
        groupPath: 'Hydrology',
        type: LayerType.WMS,
        visible: false,
        opacity: 0.6,
        zIndex: 10,
        legend_url: '',
        abstract: 'Rivers in the study area'
      },
      {
        id: 'test_layer_2',
        name: 'Lakes',
        originalName: 'Hydrology/Lakes',
        groupPath: 'Hydrology',
        type: LayerType.WMS,
        visible: false,
        opacity: 0.6,
        zIndex: 11,
        legend_url: '',
        abstract: 'Lakes in the study area'
      },
      {
        id: 'test_layer_3',
        name: 'Roads',
        originalName: 'Infrastructure/Roads',
        groupPath: 'Infrastructure',
        type: LayerType.WMS,
        visible: false,
        opacity: 0.6,
        zIndex: 12,
        legend_url: '',
        abstract: 'Road network'
      },
      {
        id: 'test_layer_4',
        name: 'Buildings',
        originalName: 'Infrastructure/Buildings',
        groupPath: 'Infrastructure',
        type: LayerType.WMS,
        visible: false,
        opacity: 0.6,
        zIndex: 13,
        legend_url: '',
        abstract: 'Building footprints'
      },
      {
        id: 'test_layer_5',
        name: 'Elevation',
        originalName: 'Elevation',
        type: LayerType.WMS,
        visible: false,
        opacity: 0.6,
        zIndex: 14,
        legend_url: '',
        abstract: 'Digital elevation model'
      }
    ];

    organizeLayersIntoGroups(testLayers);
    testLayers.forEach(layer => {
      layers.value.push(layer);
    });

    console.log('Added test layers:', layers.value);
    console.log('Test root groups:', rootGroups.value);
    console.log('Test ungrouped layers:', ungroupedLayers.value);
  };

  const organizeLayersIntoGroups = (fetchedLayers: GeoLayer[]): void => {
    groups.value = [];
    const groupMap = new Map<string, GroupLayer>();

    fetchedLayers.forEach(layer => {
      if (!layer.groupPath) return;

      const groupNames = layer.groupPath.split('/');
      let currentPath = '';
      let parentGroup: GroupLayer | undefined;
      let level = 0;

      groupNames.forEach((groupName: string) => {
        currentPath = currentPath ? `${currentPath}/${groupName}` : groupName;
        let group = groupMap.get(currentPath);

        if (!group) {
          group = {
            id: `group_${currentPath.replace(/\//g, '_')}`,
            name: groupName,
            children: [] as (GeoLayer | GroupLayer)[],
            collapsed: false, // Start expanded for better UX
            visible: false,
            level,
            parent: parentGroup,
          } as GroupLayer;
          groups.value.push(group);
          groupMap.set(currentPath, group);
          if (parentGroup) {
            parentGroup.children.push(group);
          }
        }
        parentGroup = group;
        level++;
      });

      layer.parent = parentGroup;
      if (parentGroup) {
        parentGroup.children.push(layer);
      }
    });
  };

  const toggleGroupCollapse = (groupId: string): void => {
    const group = groups.value.find(g => g.id === groupId);
    if (group) {
      group.collapsed = !group.collapsed;
    }
  };

  const toggleGroupVisibility = (groupId: string): void => {
    const group = groups.value.find(g => g.id === groupId);
    if (!group) return;
    
    const newVisibility = !group.visible;
    group.visible = newVisibility;
    updateChildrenVisibility(group as GroupLayer, newVisibility);
  };

  const updateChildrenVisibility = (group: GroupLayer, visible: boolean): void => {
    group.children.forEach((child: GeoLayer | GroupLayer) => {
      if ('children' in child) {
        const childGroup = child as GroupLayer;
        childGroup.visible = visible;
        updateChildrenVisibility(childGroup, visible);
      } else {
        const layer = child as GeoLayer;
        toggleLayerVisibility(layer.id, visible);
      }
    });
  };

  const toggleLayerVisibility = (layerId: string, visible?: boolean): void => {
    const layer = layers.value.find(l => l.id === layerId);
    if (!layer || !mapManager) return;
    
    const newVisibility = visible !== undefined ? visible : !layer.visible;
    layer.visible = newVisibility;
    mapManager.toggleLayerVisibility(layerId, newVisibility);
  };

  const updateLayerOpacity = (layerId: string, opacity: number): void => {
    const layer = layers.value.find(l => l.id === layerId);
    if (!layer || !mapManager) return;
    
    layer.opacity = opacity;
    mapManager.updateLayerOpacity(layerId, opacity);
  };

  // Initialize layers on mount
  onMounted(() => {
    fetchLayers();
  });

  return {
    layers,
    groups,
    rootGroups,
    ungroupedLayers,
    isLoading,
    error,
    fetchLayers,
    toggleGroupCollapse,
    toggleGroupVisibility,
    toggleLayerVisibility,
    updateLayerOpacity
  };
}
