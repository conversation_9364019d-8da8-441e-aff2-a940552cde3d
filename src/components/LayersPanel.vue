<template>
  <div 
    v-if="visible" 
    ref="panelRef"
    class="sidebar"
    :class="{ dragging: isDragging }"
  >
    <div class="sidebar-header">
      <div
        class="drag-handle"
        @mousedown="startDrag"
        @touchstart="startDragTouch"
        title="Drag to move panel"
      >
        <i class="fas fa-grip-vertical"></i>
      </div>
      <h2>Capas</h2>
      <button
        class="close-button"
        @click="$emit('close')"
        :aria-label="'Close layers panel'"
      >
        <i class="fas fa-times"></i>
      </button>
    </div>
    <div id="layer-control" class="sidebar-content">
      <div v-if="isLoading" class="loading">Cargando capas...</div>
      <div v-else-if="error" class="error">{{ error }}</div>
      <div v-else-if="(!rootGroups || rootGroups.length === 0) && (!ungroupedLayers || ungroupedLayers.length === 0)" class="no-layers">
        <p>No se encontró capas en el workspace.</p>
        <p><small>Debug: rootGroups = {{ rootGroups?.length || 0 }}, ungroupedLayers = {{ ungroupedLayers?.length || 0 }}</small></p>
      </div>
      <div v-else class="layer-tree">
        <div class="debug-info">
          <small>Found {{ (rootGroups?.length || 0) }} groups and {{ (ungroupedLayers?.length || 0) }} ungrouped layers</small>
        </div>

        <!-- Render root groups -->
        <LayerGroup
          v-for="group in rootGroups"
          :key="group.id"
          :group="group"
          @toggle-group-collapse="toggleGroupCollapse"
          @toggle-group-visibility="toggleGroupVisibility"
          @toggle-layer-visibility="toggleLayerVisibility"
        />

        <!-- Render ungrouped layers -->
        <LayerItem
          v-for="layer in ungroupedLayers"
          :key="layer.id"
          :layer="layer"
          @toggle-visibility="toggleLayerVisibility"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, inject, type Ref } from 'vue';
import { useDraggable } from '../composables/useDraggable';
import LayerGroup from './LayerGroup.vue';
import LayerItem from './LayerItem.vue';
import type { GeoLayer, GroupLayer } from '../types';

// Props and emits
defineProps<{
  visible: boolean;
}>();

defineEmits<{
  close: [];
}>();

// Template refs
const panelRef = ref<HTMLElement>();

// Get services from injection
const layerManager = inject<any>('layerManager');
const layers = inject<Ref<GeoLayer[]>>('layers');
const rootGroups = inject<Ref<GroupLayer[]>>('rootGroups');
const ungroupedLayers = inject<Ref<GeoLayer[]>>('ungroupedLayers');

// Use composables
const { isDragging, startDrag, startDragTouch } = useDraggable(panelRef);

// Loading and error states
const isLoading = ref(false);
const error = ref('');

// Layer management functions
const toggleGroupCollapse = (groupId: string): void => {
  console.log('LayersPanel: Received toggle-group-collapse event for:', groupId);
  const layerManager = getLayerManager?.();
  if (layerManager && layerManager.toggleGroupCollapse) {
    console.log('LayersPanel: Calling layerManager.toggleGroupCollapse');
    layerManager.toggleGroupCollapse(groupId);
  } else {
    console.log('LayersPanel: layerManager or toggleGroupCollapse method not available');
  }
};

const toggleGroupVisibility = (groupId: string): void => {
  const layerManager = getLayerManager?.();
  if (layerManager && layerManager.toggleGroupVisibility) {
    layerManager.toggleGroupVisibility(groupId);
  }
};

const toggleLayerVisibility = (layerId: string, visible?: boolean): void => {
  const layerManager = getLayerManager?.();
  if (layerManager && layerManager.toggleLayerVisibility) {
    layerManager.toggleLayerVisibility(layerId, visible);
  }
};
</script>

<style scoped>
.sidebar {
  position: absolute;
  top: 10px;
  left: 90px;
  width: 320px;
  height: fit-content;
  max-height: calc(100vh - 200px);
  background-color: #0f5397;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.sidebar.dragging {
  user-select: none;
  cursor: move;
}

.sidebar-header {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  background-color: #0f5397;
  color: white;
  border-radius: 4px 4px 0 0;
  gap: 8px;
}

.drag-handle {
  cursor: move;
  color: rgba(255, 255, 255, 0.7);
  padding: 4px;
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.2s ease;
}

.drag-handle:hover {
  color: white;
  background-color: rgba(255, 255, 255, 0.1);
}

.sidebar-header h2 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  flex: 1;
}

.close-button {
  width: 24px;
  height: 24px;
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 2px;
  color: white;
}

.close-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.close-button:focus {
  outline: 2px solid #ffffff;
  outline-offset: 1px;
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
  background-color: white;
  border-radius: 0 0 4px 4px;
  max-height: 400px;
}

.layer-tree {
  padding: 4px 0;
}

.debug-info {
  padding: 6px 8px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  font-size: 11px;
  color: #6c757d;
}

.loading,
.error,
.no-layers {
  padding: 20px;
  text-align: center;
  font-size: 14px;
  color: #6c757d;
}

.error {
  color: #dc3545;
}

/* Responsive design */
@media (max-width: 768px) {
  .sidebar {
    width: 280px;
    left: 50px;
    top: 8px;
    max-height: calc(100vh - 150px);
  }
}

@media (max-width: 480px) {
  .sidebar {
    width: calc(100vw - 20px);
    left: 10px;
    right: 10px;
    top: 5px;
    max-height: calc(100vh - 100px);
  }
}
</style>
